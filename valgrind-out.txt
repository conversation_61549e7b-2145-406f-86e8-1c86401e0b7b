==12673== Memcheck, a memory error detector
==12673== Copyright (C) 2002-2017, and GNU GPL'd, by <PERSON> et al.
==12673== Using Valgrind-3.18.1-42b08ed5bd-20211015 and LibVEX; rerun with -h for copyright info
==12673== Command: ./humangl
==12673== Parent PID: 5032
==12673== 
--12673-- 
--12673-- Valgrind options:
--12673--    --leak-check=full
--12673--    --show-leak-kinds=all
--12673--    --track-origins=yes
--12673--    --verbose
--12673--    --log-file=valgrind-out.txt
--12673-- Contents of /proc/version:
--12673--   Linux version 6.8.0-64-generic (buildd@lcy02-amd64-101) (x86_64-linux-gnu-gcc-12 (Ubuntu 12.3.0-1ubuntu1~22.04) 12.3.0, GNU ld (GNU Binutils for Ubuntu) 2.38) #67~22.04.1-Ubuntu SMP PREEMPT_DYNAMIC Tue Jun 24 15:19:46 UTC 2
--12673-- 
--12673-- Arch and hwcaps: AMD64, LittleEndian, amd64-cx16-lzcnt-rdtscp-sse3-ssse3-avx-avx2-bmi-f16c-rdrand-rdseed
--12673-- Page sizes: currently 4096, max supported 4096
--12673-- Valgrind library directory: /usr/libexec/valgrind
--12673-- Reading syms from /home/<USER>/Documents/Campus/Post/General/human/humangl
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2
--12673--   Considering /usr/lib/debug/.build-id/9c/b53985768bb99f138f48655f7b8bf7e420d13d.debug ..
--12673--   .. build-id is valid
--12673-- Reading syms from /usr/libexec/valgrind/memcheck-amd64-linux
--12673--    object doesn't have a symbol table
--12673--    object doesn't have a dynamic symbol table
--12673-- Scheduler: using generic scheduler lock implementation.
--12673-- Reading suppressions file: /usr/libexec/valgrind/default.supp
==12673== embedded gdbserver: reading from /tmp/vgdb-pipe-from-vgdb-to-12673-by-rperez-t-on-???
==12673== embedded gdbserver: writing to   /tmp/vgdb-pipe-to-vgdb-from-12673-by-rperez-t-on-???
==12673== embedded gdbserver: shared mem   /tmp/vgdb-pipe-shared-mem-vgdb-12673-by-rperez-t-on-???
==12673== 
==12673== TO CONTROL THIS PROCESS USING vgdb (which you probably
==12673== don't want to do, unless you know exactly what you're doing,
==12673== or are doing some strange experiment):
==12673==   /usr/bin/vgdb --pid=12673 ...command...
==12673== 
==12673== TO DEBUG THIS PROCESS USING GDB: start GDB like this
==12673==   /path/to/gdb ./humangl
==12673== and then give GDB the following command
==12673==   target remote | /usr/bin/vgdb --pid=12673
==12673== --pid is optional if only one valgrind process is running
==12673== 
--12673-- REDIR: 0x402aa40 (ld-linux-x86-64.so.2:strlen) redirected to 0x580bcec2 (???)
--12673-- REDIR: 0x402a810 (ld-linux-x86-64.so.2:index) redirected to 0x580bcedc (???)
--12673-- Reading syms from /usr/libexec/valgrind/vgpreload_core-amd64-linux.so
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so
--12673--    object doesn't have a symbol table
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x0402aa40 (strlen              ) R-> (0000.0) 0x580bcec2 ???
--12673--     new: 0x0402aa40 (strlen              ) R-> (2007.0) 0x0484ee30 strlen
--12673-- REDIR: 0x4027220 (ld-linux-x86-64.so.2:strcmp) redirected to 0x484fcd0 (strcmp)
--12673-- REDIR: 0x402afa0 (ld-linux-x86-64.so.2:mempcpy) redirected to 0x4853840 (mempcpy)
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libGL.so.1.7.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.30
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libm.so.6
--12673--   Considering /usr/lib/debug/.build-id/a3/ad9bb40b4907e509e4404cb972645c19675ca3.debug ..
--12673--   .. build-id is valid
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libgcc_s.so.1
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libc.so.6
--12673--   Considering /usr/lib/debug/.build-id/d5/197096f709801829b118af1b7cf6631efa2dcd.debug ..
--12673--   .. build-id is valid
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04e73c60 (memalign            ) R-> (1011.0) 0x0484e080 memalign
--12673--     new: 0x04e73c60 (memalign            ) R-> (1017.0) 0x0484e050 aligned_alloc
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04e73c60 (memalign            ) R-> (1011.0) 0x0484e080 memalign
--12673--     new: 0x04e73c60 (memalign            ) R-> (1017.0) 0x0484e020 aligned_alloc
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04e73c60 (memalign            ) R-> (1011.0) 0x0484e080 memalign
--12673--     new: 0x04e73c60 (memalign            ) R-> (1017.0) 0x0484e050 aligned_alloc
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04e73c60 (memalign            ) R-> (1011.0) 0x0484e080 memalign
--12673--     new: 0x04e73c60 (memalign            ) R-> (1017.0) 0x0484e020 aligned_alloc
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libasound.so.2.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libpulse.so.0.24.1
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXext.so.6.4.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXcursor.so.1.0.2
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXinerama.so.1.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXi.so.6.1.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXfixes.so.3.1.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXrandr.so.2.2.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXss.so.1.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXxf86vm.so.1.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libdrm.so.2.4.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libgbm.so.1.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libwayland-egl.so.1.21.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libwayland-client.so.0.21.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libwayland-cursor.so.0.21.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libxkbcommon.so.0.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libdecor-0.so.0.100.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libGLdispatch.so.0.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libGLX.so.0.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/pulseaudio/libpulsecommon-15.99.so
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libxcb.so.1.1.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXrender.so.1.3.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libwayland-server.so.0.21.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libexpat.so.1.8.7
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libxcb-randr.so.0.1.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libffi.so.8.1.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libsndfile.so.1.0.31
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libX11-xcb.so.1.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libsystemd.so.0.32.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libasyncns.so.0.3.1
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libapparmor.so.1.8.2
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXau.so.6.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libXdmcp.so.6.0.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libFLAC.so.8.3.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libvorbis.so.0.4.9
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libvorbisenc.so.2.0.12
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libopus.so.0.8.0
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libogg.so.0.8.5
--12673--   Considering /usr/lib/x86_64-linux-gnu/libogg.so.0.8.5 ..
--12673--   .. CRC mismatch (computed 2f010366 wanted 3991f6dc)
--12673--   Considering /lib/x86_64-linux-gnu/libogg.so.0.8.5 ..
--12673--   .. CRC mismatch (computed 2f010366 wanted 3991f6dc)
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/liblzma.so.5.2.5
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libzstd.so.1.4.8
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/liblz4.so.1.9.3
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libcap.so.2.44
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libgcrypt.so.20.3.4
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libbsd.so.0.11.5
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libgpg-error.so.0.32.1
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libmd.so.0.0.5
--12673--    object doesn't have a symbol table
--12673-- REDIR: 0x4e76720 (libc.so.6:strnlen) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e769e0 (libc.so.6:strpbrk) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e762e0 (libc.so.6:strcmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e94cd0 (libc.so.6:wcsnlen) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e776d0 (libc.so.6:memset) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e93540 (libc.so.6:wcslen) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77c10 (libc.so.6:memcpy@@GLIBC_2.14) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e93370 (libc.so.6:wcschr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76260 (libc.so.6:index) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76960 (libc.so.6:rindex) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e775a0 (libc.so.6:memmove) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
==12673== Preferring higher priority redirection:
--12673--     old: 0x04f6e7c0 (__memcpy_avx_unalign) R-> (2018.0) 0x04850f90 __memcpy_avx_unaligned_erms
--12673--     new: 0x04f6e7c0 (__memcpy_avx_unalign) R-> (2018.1) 0x04852880 memmove
--12673-- REDIR: 0x4e933f0 (libc.so.6:wcscmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e779e0 (libc.so.6:stpncpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e939b0 (libc.so.6:wmemchr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76830 (libc.so.6:strncmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77a70 (libc.so.6:strcasecmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76420 (libc.so.6:strcspn) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e93470 (libc.so.6:wcscpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e761d0 (libc.so.6:strcat) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77b60 (libc.so.6:strncasecmp_l) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77480 (libc.so.6:bcmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e7f570 (libc.so.6:memrchr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e79010 (libc.so.6:strchrnul) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76390 (libc.so.6:strcpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77b10 (libc.so.6:strcasecmp_l) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e766a0 (libc.so.6:strlen) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e768d0 (libc.so.6:strncpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77950 (libc.so.6:stpcpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e773f0 (libc.so.6:memchr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e76b00 (libc.so.6:strspn) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e777d0 (libc.so.6:mempcpy) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77ac0 (libc.so.6:strncasecmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e78f80 (libc.so.6:rawmemchr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4f02280 (libc.so.6:__memcpy_chk) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e77310 (libc.so.6:strstr) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4e767a0 (libc.so.6:strncat) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
--12673-- REDIR: 0x4f023b0 (libc.so.6:__memmove_chk) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04f6e790 (__memcpy_chk_avx_una) R-> (2030.0) 0x04853940 __memcpy_chk
--12673--     new: 0x04f6e790 (__memcpy_chk_avx_una) R-> (2024.0) 0x048532c0 __memmove_chk
--12673-- REDIR: 0x4e93620 (libc.so.6:wcsncmp) redirected to 0x483f220 (_vgnU_ifunc_wrapper)
==12673== WARNING: new redirection conflicts with existing -- ignoring it
--12673--     old: 0x04f6e790 (__memcpy_chk_avx_una) R-> (2030.0) 0x04853940 __memcpy_chk
--12673--     new: 0x04f6e790 (__memcpy_chk_avx_una) R-> (2024.0) 0x048532c0 __memmove_chk
--12673-- REDIR: 0x4f6b610 (libc.so.6:__strrchr_avx2) redirected to 0x484e810 (rindex)
--12673-- REDIR: 0x4f6b7e0 (libc.so.6:__strlen_avx2) redirected to 0x484ed10 (strlen)
--12673-- REDIR: 0x4e730a0 (libc.so.6:malloc) redirected to 0x4848820 (malloc)
--12673-- REDIR: 0x4f6e7c0 (libc.so.6:__memcpy_avx_unaligned_erms) redirected to 0x4852880 (memmove)
--12673-- REDIR: 0x4f66940 (libc.so.6:__strcmp_avx2) redirected to 0x484fbd0 (strcmp)
--12673-- REDIR: 0x4e73740 (libc.so.6:realloc) redirected to 0x484dc50 (realloc)
--12673-- REDIR: 0x4f67a80 (libc.so.6:__memcmp_avx2_movbe) redirected to 0x4852010 (bcmp)
--12673-- REDIR: 0x4e74520 (libc.so.6:calloc) redirected to 0x484d9d0 (calloc)
--12673-- REDIR: 0x4f6ef80 (libc.so.6:__memset_avx2_unaligned_erms) redirected to 0x4852770 (memset)
--12673-- REDIR: 0x4b47970 (libstdc++.so.6:operator new(unsigned long)) redirected to 0x4848fa0 (operator new(unsigned long))
--12673-- REDIR: 0x4b45c10 (libstdc++.so.6:operator delete(void*)) redirected to 0x484b840 (operator delete(void*))
--12673-- REDIR: 0x4b45c20 (libstdc++.so.6:operator delete(void*, unsigned long)) redirected to 0x484bb00 (operator delete(void*, unsigned long))
--12673-- REDIR: 0x4e733e0 (libc.so.6:free) redirected to 0x484b210 (free)
--12673-- REDIR: 0x4f66d80 (libc.so.6:__strncmp_avx2) redirected to 0x484f3e0 (strncmp)
--12673-- REDIR: 0x4f6d040 (libc.so.6:__strncpy_avx2) redirected to 0x484efe0 (strncpy)
--12673-- REDIR: 0x4f6b400 (libc.so.6:__strchrnul_avx2) redirected to 0x4853330 (strchrnul)
--12673-- REDIR: 0x4f027e0 (libc.so.6:__strcpy_chk) redirected to 0x48533a0 (__strcpy_chk)
--12673-- REDIR: 0x4e76fc0 (libc.so.6:__GI_strstr) redirected to 0x4853ae0 (__strstr_sse2)
--12673-- REDIR: 0x4f6ccb0 (libc.so.6:__strcpy_avx2) redirected to 0x484ee60 (strcpy)
--12673-- REDIR: 0x4e92180 (libc.so.6:__strstr_sse2_unaligned) redirected to 0x4853a50 (strstr)
--12673-- REDIR: 0x4f6b180 (libc.so.6:__strchr_avx2) redirected to 0x484e9f0 (index)
--12673-- REDIR: 0x4f6e780 (libc.so.6:__mempcpy_avx_unaligned_erms) redirected to 0x4853440 (mempcpy)
--12673-- REDIR: 0x4f672c0 (libc.so.6:__memchr_avx2) redirected to 0x484fd50 (memchr)
--12673-- REDIR: 0x4f6d9a0 (libc.so.6:__stpcpy_avx2) redirected to 0x4852130 (stpcpy)
--12673-- REDIR: 0x4f6bc30 (libc.so.6:__strcat_avx2) redirected to 0x484ea20 (strcat)
--12673-- REDIR: 0x4f66800 (libc.so.6:__strspn_sse42) redirected to 0x4853d50 (strspn)
--12673-- REDIR: 0x4f665a0 (libc.so.6:__strcspn_sse42) redirected to 0x4853c70 (strcspn)
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libnvidia-glsi.so.535.247.01
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libnvidia-tls.so.535.247.01
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libdl.so.2
--12673--   Considering /usr/lib/debug/.build-id/c7/1c8c73dd8b7323bd6190420f0ebc4301e93186.debug ..
--12673--   .. build-id is valid
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libpthread.so.0
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/librt.so.1
--12673--   Considering /usr/lib/debug/.build-id/cb/5b17a9bdf060b73ecac80f77fb2c535ef84612.debug ..
--12673--   .. build-id is valid
--12673-- REDIR: 0x4e750e0 (libc.so.6:posix_memalign) redirected to 0x484df60 (posix_memalign)
--12673-- REDIR: 0x4f68130 (libc.so.6:__strcasecmp_avx) redirected to 0x484f560 (strcasecmp)
--12673-- REDIR: 0x4e92870 (libc.so.6:memcpy@GLIBC_2.2.5) redirected to 0x484fe00 (memcpy@GLIBC_2.2.5)
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libnvidia-glvkspirv.so.535.247.01
--12673--    object doesn't have a symbol table
--12673-- Reading syms from /usr/lib/x86_64-linux-gnu/libxcb-glx.so.0.0.0
--12673--    object doesn't have a symbol table
--12673-- REDIR: 0x4f6c2a0 (libc.so.6:__strncat_avx2) redirected to 0x484ebc0 (strncat)
==12673== Warning: noted but unhandled ioctl 0x644f with no size/direction hints.
==12673==    This could cause spurious value errors to appear.
==12673==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.nvidia_drv.XXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.nvidia_drv.XXXXXX (deleted):
--12673-- failed to stat64/stat this file
==12673== Warning: noted but unhandled ioctl 0x6444 with no size/direction hints.
==12673==    This could cause spurious value errors to appear.
==12673==    See README_MISSING_SYSCALL_OR_IOCTL for guidance on writing a proper wrapper.
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
--12673-- WARNING: Serious error when reading debug info
--12673-- When reading debug info from /memfd:/.glXXXXXX (deleted):
--12673-- failed to stat64/stat this file
==12673== 
==12673== HEAP SUMMARY:
==12673==     in use at exit: 79,823 bytes in 1,074 blocks
==12673==   total heap usage: 27,154 allocs, 26,080 frees, 1,828,358,108 bytes allocated
==12673== 
==12673== Searching for pointers to 1,074 not-freed blocks
==12673== Checked 2,773,216 bytes
==12673== 
==12673== 0 bytes in 5 blocks are definitely lost in loss record 1 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4005A22: malloc (rtld-malloc.h:56)
==12673==    by 0x4005A22: _dl_find_object_update (dl-find_object.c:791)
==12673==    by 0x400ECCF: dl_open_worker_begin (dl-open.c:735)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673==    by 0x4E5E12D: _dlerror_run (dlerror.c:138)
==12673==    by 0x4E5E6C7: dlopen_implementation (dlopen.c:71)
==12673==    by 0x4E5E6C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==12673== 
==12673== 2 bytes in 1 blocks are still reachable in loss record 2 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519F327: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 2 bytes in 1 blocks are still reachable in loss record 3 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519E791: _XlcResolveLocaleName (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5497: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 6 bytes in 1 blocks are still reachable in loss record 4 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x51A558E: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 7 bytes in 1 blocks are still reachable in loss record 5 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x51A558E: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 7 bytes in 1 blocks are still reachable in loss record 6 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55298F3: _dbus_strdup (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F758: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 7 of 194
==12673==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5522379: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550C56F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F5B1: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 8 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51B0C49: _XimOpenIM (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966345: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 9 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A601: _dbus_string_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527826: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 10 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A601: _dbus_string_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552783E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 11 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A601: _dbus_string_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55327A8: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 8 bytes in 1 blocks are still reachable in loss record 12 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A601: _dbus_string_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55327D4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 12 bytes in 1 blocks are still reachable in loss record 13 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519F327: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 14 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AA5FD: _XlcAddLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52F5: _XlcInitLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC660: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 15 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AA5FD: _XlcAddLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5303: _XlcInitLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC660: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 16 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AA5FD: _XlcAddLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC660: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 17 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198364: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3909: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 18 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AC697: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 19 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AC697: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 16 bytes in 1 blocks are still reachable in loss record 20 of 194
==12673==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552E5A2: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550C3FF: dbus_connection_set_data (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550C5D4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F5B1: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 16 bytes in 2 blocks are still reachable in loss record 21 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A3E16: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 17 bytes in 1 blocks are still reachable in loss record 22 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51AA535: _XlcDefaultMapModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC77A: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 23 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552172A: dbus_threads_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4980893: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 24 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5530F3D: _dbus_register_shutdown_func (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F8BD: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 25 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5530F3D: _dbus_register_shutdown_func (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551CE35: dbus_message_unref (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521B0E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3D2: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 26 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198159: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 27 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198159: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 28 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519E6A3: _XlcResolveLocaleName (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5497: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 29 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5530F3D: _dbus_register_shutdown_func (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E689: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 30 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x550CC67: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 24 bytes in 1 blocks are still reachable in loss record 31 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x550C5B1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F5B1: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 29 bytes in 1 blocks are still reachable in loss record 32 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55298F3: _dbus_strdup (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550FCE7: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 29 bytes in 1 blocks are still reachable in loss record 33 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55298F3: _dbus_strdup (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550FCAA: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 31 bytes in 1 blocks are still reachable in loss record 34 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AB5F: _dbus_string_append (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55327E7: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 32 bytes in 1 blocks are still reachable in loss record 35 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519B8C3: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196428: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DF0: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 32 bytes in 1 blocks are still reachable in loss record 36 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4013E4D: malloc (rtld-malloc.h:56)
==12673==    by 0x4013E4D: allocate_dtv_entry (dl-tls.c:684)
==12673==    by 0x4013E4D: allocate_and_init (dl-tls.c:709)
==12673==    by 0x4013E4D: tls_get_addr_tail (dl-tls.c:907)
==12673==    by 0x401820B: __tls_get_addr (tls_get_addr.S:55)
==12673==    by 0xCB5AE61: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glvkspirv.so.535.247.01)
==12673==    by 0xB60E23E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glvkspirv.so.535.247.01)
==12673==    by 0xD1502C5: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glvkspirv.so.535.247.01)
==12673==    by 0xB580EE2: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glvkspirv.so.535.247.01)
==12673==    by 0x6: ???
==12673==    by 0x4006439: call_init.part.0 (dl-init.c:56)
==12673==    by 0x4006567: call_init (dl-init.c:33)
==12673==    by 0x4006567: _dl_init (dl-init.c:117)
==12673==    by 0x4F42AF4: _dl_catch_exception (dl-error-skeleton.c:182)
==12673==    by 0x400DFF5: dl_open_worker (dl-open.c:808)
==12673==    by 0x400DFF5: dl_open_worker (dl-open.c:771)
==12673== 
==12673== 32 bytes in 1 blocks are still reachable in loss record 37 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552ADF0: _dbus_string_copy (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5508177: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BF9: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 38 bytes in 1 blocks are still reachable in loss record 38 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55298F3: _dbus_strdup (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550FAD9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 38 bytes in 1 blocks are still reachable in loss record 39 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A99D: _dbus_string_copy_data (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527AED: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 40 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529AD7: _dbus_mem_pool_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5530706: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x553076F: _dbus_list_prepend (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55307DC: _dbus_list_append (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55078F8: dbus_parse_address (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E46B: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F900: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 41 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51789E9: XInitThreads (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x496CE75: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493FA48: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 42 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5178A06: XInitThreads (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x496CE75: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493FA48: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 43 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5178A23: XInitThreads (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x496CE75: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493FA48: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 44 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A29A2: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 45 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A29A2: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 46 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529AD7: _dbus_mem_pool_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528E46: _dbus_hash_table_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E66F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 47 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552D045: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552186C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55278CC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 48 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529AD7: _dbus_mem_pool_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528E46: _dbus_hash_table_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550C9FE: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 49 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552D045: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CB6C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 50 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55218B1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CB82: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 51 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55218B1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CB94: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 52 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552D045: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CBF4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 53 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552D045: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552186C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CC4C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 40 bytes in 1 blocks are still reachable in loss record 54 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C272: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x497DD4F: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x497DF47: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 47 bytes in 5 blocks are still reachable in loss record 55 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A08F2: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 48 bytes in 1 blocks are still reachable in loss record 56 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x550C9C9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 48 bytes in 1 blocks are still reachable in loss record 57 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x550C9DF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 48 bytes in 1 blocks are still reachable in loss record 58 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5521915: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CBA6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 48 bytes in 1 blocks are still reachable in loss record 59 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5521915: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CBB8: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 56 bytes in 1 blocks are still reachable in loss record 60 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5507801: _dbus_credentials_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55327F0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 56 bytes in 1 blocks are still reachable in loss record 61 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5507801: _dbus_credentials_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55327FF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 56 bytes in 1 blocks are still reachable in loss record 62 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5507801: _dbus_credentials_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x553280E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 56 bytes in 1 blocks are still reachable in loss record 63 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5507801: _dbus_credentials_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55278E9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 64 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552ADF0: _dbus_string_copy (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55099EE: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509EFC: _dbus_auth_do_work (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5526178: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5526CB4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C42: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521D53: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 65 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5182126: _XrmInternalStringToQuark (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5195CB3: XrmInitialize (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B5B: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 66 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0CB8: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 67 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0CCE: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 68 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0CE4: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 69 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0CFA: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 70 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B09: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 71 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B21: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 72 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B39: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 73 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B55: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 74 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B6D: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 75 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B85: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 76 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3B9D: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 77 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3BB5: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 78 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3BCD: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 79 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3BE5: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 80 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3BFD: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 81 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD5B: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 82 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD7A: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 83 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD92: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 84 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDAA: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 85 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDC2: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 86 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDE1: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 87 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3C05: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 88 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABE5C: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 89 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABE74: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 90 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABE8C: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 91 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABEA4: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 92 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABEBC: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 93 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABED4: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 94 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABEEC: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 95 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF04: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 96 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF23: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 97 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF3B: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 98 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF53: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 99 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF72: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 100 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABF8A: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 101 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABFA2: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 102 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABFBA: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 103 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABFD2: _XlcAddUtf8LocaleConverters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 104 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4927: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 105 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD5B: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 106 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD7A: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 107 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABD92: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 108 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDAA: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 109 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDC2: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 110 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ABDE1: _XlcAddUtf8Converters (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 111 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0B96: _XlcSetConverter (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C4914: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 112 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400F21D: malloc (rtld-malloc.h:56)
==12673==    by 0x400F21D: resize_scopes (dl-open.c:295)
==12673==    by 0x400F21D: dl_open_worker_begin (dl-open.c:707)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673==    by 0x4E5E12D: _dlerror_run (dlerror.c:138)
==12673==    by 0x4E5E6C7: dlopen_implementation (dlopen.c:71)
==12673==    by 0x4E5E6C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==12673==    by 0x7FF093F: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 113 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552035C: _dbus_watch_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552785C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 114 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552035C: _dbus_watch_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527883: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 115 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552E27E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CC8E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 64 bytes in 1 blocks are still reachable in loss record 116 of 194
==12673==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x55146F0: _dbus_message_loader_get_unix_fds (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528965: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 69 bytes in 5 blocks are still reachable in loss record 117 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0751: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3DDE: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 80 bytes in 2 blocks are still reachable in loss record 118 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C272: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 80 bytes in 3 blocks are still reachable in loss record 119 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A3D35: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 80 bytes in 5 blocks are still reachable in loss record 120 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0619: _XlcAddCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3E00: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 88 bytes in 1 blocks are still reachable in loss record 121 of 194
==12673==    at 0x48487A9: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x48F7D0F: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x48FA6A6: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x48FA80B: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F084: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 88 bytes in 1 blocks are still reachable in loss record 122 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552182F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55278CC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 88 bytes in 1 blocks are still reachable in loss record 123 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552182F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CC4C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 88 bytes in 1 blocks are indirectly lost in loss record 124 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531ED1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E156: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 89 bytes in 5 blocks are still reachable in loss record 125 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A06E4: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3DDE: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 96 bytes in 1 blocks are still reachable in loss record 126 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5514604: _dbus_message_loader_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527899: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 104 bytes in 3 blocks are still reachable in loss record 127 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198BE5: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 112 bytes in 1 blocks are still reachable in loss record 128 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5528E2F: _dbus_hash_table_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E66F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 112 bytes in 1 blocks are still reachable in loss record 129 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5528E2F: _dbus_hash_table_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550C9FE: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 112 bytes in 14 blocks are still reachable in loss record 130 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A3E16: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 132 bytes in 1 blocks are still reachable in loss record 131 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x48FA816: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F084: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 135 bytes in 3 blocks are still reachable in loss record 132 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x40271DF: malloc (rtld-malloc.h:56)
==12673==    by 0x40271DF: strdup (strdup.c:42)
==12673==    by 0x4016A66: _dl_load_cache_lookup (dl-cache.c:527)
==12673==    by 0x400A981: _dl_map_object (dl-load.c:2193)
==12673==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673== 
==12673== 135 bytes in 3 blocks are still reachable in loss record 133 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400DD20: malloc (rtld-malloc.h:56)
==12673==    by 0x400DD20: _dl_new_object (dl-object.c:199)
==12673==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==12673==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==12673==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673== 
==12673== 136 bytes in 1 blocks are still reachable in loss record 134 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198364: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3909: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 142 bytes in 18 blocks are still reachable in loss record 135 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198CF6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 160 bytes in 1 blocks are still reachable in loss record 136 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531ED1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E156: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55120A9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4982173: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 160 bytes in 2 blocks are still reachable in loss record 137 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198335: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3909: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 168 bytes in 1 blocks are still reachable in loss record 138 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519818F: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 168 bytes in 1 blocks are still reachable in loss record 139 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519818F: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 176 bytes in 1 blocks are still reachable in loss record 140 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5518E52: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519311: _dbus_type_writer_recurse (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551943F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551965E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519743: dbus_message_set_reply_serial (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D52A: dbus_message_new_error (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550EFC9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 176 bytes in 1 blocks are still reachable in loss record 141 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198174: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 176 bytes in 1 blocks are still reachable in loss record 142 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198174: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2B9: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 182 bytes in 3 blocks are still reachable in loss record 143 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198C1D: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 184 bytes in 1 blocks are still reachable in loss record 144 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C16F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D005: dbus_message_new_method_call (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F63C: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 184 bytes in 1 blocks are still reachable in loss record 145 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C16F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4981767: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 184 bytes in 1 blocks are still reachable in loss record 146 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C16F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x497DD4F: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x497DF47: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 216 bytes in 18 blocks are still reachable in loss record 147 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519A449: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 223 bytes in 1 blocks are still reachable in loss record 148 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552D424: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5517B1F: _dbus_type_writer_write_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551C95B: dbus_message_iter_append_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D553: dbus_message_new_error (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550EFC9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808E3: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 223 bytes in 1 blocks are still reachable in loss record 149 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552D424: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5517B1F: _dbus_type_writer_write_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551C95B: dbus_message_iter_append_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D553: dbus_message_new_error (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550EFC9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4981767: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49819EA: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 223 bytes in 1 blocks are still reachable in loss record 150 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552D424: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5517B1F: _dbus_type_writer_write_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551C95B: dbus_message_iter_append_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D553: dbus_message_new_error (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550EFC9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x497DD4F: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49816C8: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49817C5: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 223 bytes in 1 blocks are still reachable in loss record 151 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552D424: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5517B1F: _dbus_type_writer_write_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551C95B: dbus_message_iter_append_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D553: dbus_message_new_error (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550EFC9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x8033407: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 232 bytes in 1 blocks are still reachable in loss record 152 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552780B: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 240 bytes in 1 blocks are still reachable in loss record 153 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x553274B: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5509BB1: _dbus_auth_client_new (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5527BA4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552FD9D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531679: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522964: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4B9: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 258 bytes in 6 blocks are still reachable in loss record 154 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x40271DF: malloc (rtld-malloc.h:56)
==12673==    by 0x40271DF: strdup (strdup.c:42)
==12673==    by 0x4016A66: _dl_load_cache_lookup (dl-cache.c:527)
==12673==    by 0x400A981: _dl_map_object (dl-load.c:2193)
==12673==    by 0x4003494: openaux (dl-deps.c:64)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4003C7B: _dl_map_object_deps (dl-deps.c:248)
==12673==    by 0x400EA0E: dl_open_worker_begin (dl-open.c:592)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673== 
==12673== 258 bytes in 6 blocks are still reachable in loss record 155 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400DD20: malloc (rtld-malloc.h:56)
==12673==    by 0x400DD20: _dl_new_object (dl-object.c:199)
==12673==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==12673==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==12673==    by 0x4003494: openaux (dl-deps.c:64)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4003C7B: _dl_map_object_deps (dl-deps.c:248)
==12673==    by 0x400EA0E: dl_open_worker_begin (dl-open.c:592)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673== 
==12673== 264 bytes in 1 blocks are still reachable in loss record 156 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x550CA16: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F9D6: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FCB1B3: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x7FD75DA: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x65B81B3: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65B82F7: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673==    by 0x65E2D37: ??? (in /usr/lib/x86_64-linux-gnu/libGLX_nvidia.so.535.247.01)
==12673== 
==12673== 274 bytes in 18 blocks are still reachable in loss record 157 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519A5B5: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 288 bytes in 18 blocks are still reachable in loss record 158 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198CD5: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 320 bytes in 1 blocks are still reachable in loss record 159 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552DEAA: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519311: _dbus_type_writer_recurse (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519394: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5532664: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D02D: dbus_message_new_method_call (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4982325: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x4995AD6: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x4982021: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49818B4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49819EA: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 320 bytes in 1 blocks are still reachable in loss record 160 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5518E52: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519311: _dbus_type_writer_recurse (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551943F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5532664: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551D02D: dbus_message_new_method_call (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x497DCE9: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x497DF47: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x4982FCE: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49617D0: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493FB34: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 336 bytes in 2 blocks are still reachable in loss record 161 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5531ED1: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E156: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 352 bytes in 1 blocks are still reachable in loss record 162 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552DEAA: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519311: _dbus_type_writer_recurse (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5519394: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551965E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551982E: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551C966: dbus_message_iter_append_basic (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551DDF5: dbus_message_append_args_valist (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4982356: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x498246B: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x4982F0C: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 360 bytes in 5 blocks are still reachable in loss record 163 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A06B6: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3DDE: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 404 bytes in 1 blocks are still reachable in loss record 164 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529D1C: _dbus_mem_pool_alloc (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x553071D: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x553076F: _dbus_list_prepend (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55307DC: _dbus_list_append (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55078F8: dbus_parse_address (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E46B: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F900: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x49808B2: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 404 bytes in 1 blocks are still reachable in loss record 165 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529D1C: _dbus_mem_pool_alloc (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552A1B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552A29C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5529196: _dbus_hash_table_insert_int (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F0E9: dbus_connection_send_with_reply (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3A0: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x84B3344: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 456 bytes in 1 blocks are still reachable in loss record 166 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A2947: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 520 bytes in 13 blocks are still reachable in loss record 167 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552D045: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55216FC: dbus_threads_init (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4980893: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F0C4: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 528 bytes in 1 blocks are still reachable in loss record 168 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400E146: malloc (rtld-malloc.h:56)
==12673==    by 0x400E146: add_to_global_resize (dl-open.c:152)
==12673==    by 0x400EFF7: dl_open_worker_begin (dl-open.c:716)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673==    by 0x4E5E12D: _dlerror_run (dlerror.c:138)
==12673==    by 0x4E5E6C7: dlopen_implementation (dlopen.c:71)
==12673==    by 0x4E5E6C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==12673== 
==12673== 633 bytes in 49 blocks are still reachable in loss record 169 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0751: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0ABA: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 720 bytes in 18 blocks are still reachable in loss record 170 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519A431: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C3ABB: _XlcDefaultLoader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 736 bytes in 4 blocks are still reachable in loss record 171 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C16F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 788 bytes in 1 blocks are still reachable in loss record 172 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5529D1C: _dbus_mem_pool_alloc (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55306D0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x553076F: _dbus_list_prepend (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55307DC: _dbus_list_append (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5522793: _dbus_watch_list_add_watch (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5526651: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550CD59: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550E4CF: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x4981745: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x49819EA: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x4999840: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 800 bytes in 50 blocks are still reachable in loss record 173 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A0619: _XlcAddCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0ACE: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 802 bytes in 88 blocks are still reachable in loss record 174 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198CF6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 1,028 bytes in 50 blocks are still reachable in loss record 175 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A06E4: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0ABA: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 1,092 bytes in 91 blocks are still reachable in loss record 176 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519A449: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 1,341 bytes in 91 blocks are still reachable in loss record 177 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x4E7658E: strdup (strdup.c:42)
==12673==    by 0x519A5B5: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 1,360 bytes in 17 blocks are still reachable in loss record 178 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198335: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A3909: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 1,408 bytes in 88 blocks are still reachable in loss record 179 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5198CD5: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A634: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 1,656 bytes in 9 blocks are still reachable in loss record 180 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x40162DC: calloc (rtld-malloc.h:44)
==12673==    by 0x40162DC: _dl_check_map_versions (dl-version.c:273)
==12673==    by 0x400ED13: dl_open_worker_begin (dl-open.c:600)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673==    by 0x4E5E12D: _dlerror_run (dlerror.c:138)
==12673==    by 0x4E5E6C7: dlopen_implementation (dlopen.c:71)
==12673==    by 0x4E5E6C7: dlopen@@GLIBC_2.34 (dlopen.c:81)
==12673== 
==12673== 1,698 bytes in 1 blocks are indirectly lost in loss record 181 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552AFB1: _dbus_string_copy_len (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E2CC: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x8033407: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 1,970 (184 direct, 1,786 indirect) bytes in 1 blocks are definitely lost in loss record 182 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x551C16F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x551E10A: _dbus_message_loader_queue_messages (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552681F: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552695C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x55287B0: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673== 
==12673== 2,048 bytes in 1 blocks are still reachable in loss record 183 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A2AA2: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 2,056 bytes in 1 blocks are still reachable in loss record 184 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552C057: _dbus_read (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5526EE7: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C42: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521D53: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 2,056 bytes in 1 blocks are still reachable in loss record 185 of 194
==12673==    at 0x484DCD3: realloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x552A3F4: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x552C487: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528986: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5528C9C: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550D5EC: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x5521DCB: dbus_pending_call_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F3BD: dbus_connection_send_with_reply_and_block (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F65B: dbus_bus_register (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x550F913: ??? (in /usr/lib/x86_64-linux-gnu/libdbus-1.so.3.19.13)
==12673==    by 0x801C64E: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673==    by 0x80333AF: ??? (in /usr/lib/x86_64-linux-gnu/libnvidia-glcore.so.535.247.01)
==12673== 
==12673== 2,208 bytes in 1 blocks are still reachable in loss record 186 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A2947: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 2,631 bytes in 50 blocks are still reachable in loss record 187 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A084F: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B6C: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 3,600 bytes in 50 blocks are still reachable in loss record 188 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x51A06B6: _XlcCreateDefaultCharSet (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0ABA: _XlcAddCT (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A0C6F: _XlcInitCTInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A5386: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51ACA9E: _XrmInitParseInfo (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196A84: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5196DC2: XrmGetStringDatabase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673== 
==12673== 3,640 bytes in 91 blocks are still reachable in loss record 189 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x519A431: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519A690: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A28A7: _XlcCreateLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A54CF: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52C6: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== 3,645 bytes in 3 blocks are still reachable in loss record 190 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400DA02: calloc (rtld-malloc.h:44)
==12673==    by 0x400DA02: _dl_new_object (dl-object.c:92)
==12673==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==12673==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==12673==    by 0x400E9A8: dl_open_worker_begin (dl-open.c:534)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673==    by 0x4E5E63B: dlopen_doit (dlopen.c:56)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4F42B62: _dl_catch_error (dl-error-skeleton.c:227)
==12673== 
==12673== 4,096 bytes in 1 blocks are still reachable in loss record 191 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5181F8A: _XrmInternalStringToQuark (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5195CB3: XrmInitialize (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B5B: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x13714A: main (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 7,278 bytes in 6 blocks are still reachable in loss record 192 of 194
==12673==    at 0x484DA83: calloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x400DA02: calloc (rtld-malloc.h:44)
==12673==    by 0x400DA02: _dl_new_object (dl-object.c:92)
==12673==    by 0x4008C82: _dl_map_object_from_fd (dl-load.c:1063)
==12673==    by 0x400A600: _dl_map_object (dl-load.c:2327)
==12673==    by 0x4003494: openaux (dl-deps.c:64)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x4003C7B: _dl_map_object_deps (dl-deps.c:248)
==12673==    by 0x400EA0E: dl_open_worker_begin (dl-open.c:592)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400DF99: dl_open_worker (dl-open.c:782)
==12673==    by 0x4F42A97: _dl_catch_exception (dl-error-skeleton.c:208)
==12673==    by 0x400E34D: _dl_open (dl-open.c:883)
==12673== 
==12673== 8,176 bytes in 1 blocks are still reachable in loss record 193 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5180D95: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5182105: _XrmInternalStringToQuark (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5195CB3: XrmInitialize (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173B5B: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x5173DC5: XGetDefault (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4968650: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496D9A7: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x489F426: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x10DF90: Application::initialize() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673==    by 0x10E28F: Application::run() (in /home/<USER>/Documents/Campus/Post/General/human/humangl)
==12673== 
==12673== 8,176 bytes in 1 blocks are still reachable in loss record 194 of 194
==12673==    at 0x4848899: malloc (in /usr/libexec/valgrind/vgpreload_memcheck-amd64-linux.so)
==12673==    by 0x5182299: _XrmInternalStringToQuark (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A2691: _XlcGetLocaleDataBase (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A4491: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51A52D2: ??? (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x519F2DC: _XlcCreateLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51C48C9: _XlcUtf8Loader (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC685: _XOpenLC (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x51AC752: XSetLocaleModifiers (in /usr/lib/x86_64-linux-gnu/libX11.so.6.4.0)
==12673==    by 0x4966332: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x496DAFC: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673==    by 0x493F88D: ??? (in /usr/lib/x86_64-linux-gnu/libSDL2-2.0.so.0.18.2)
==12673== 
==12673== LEAK SUMMARY:
==12673==    definitely lost: 184 bytes in 6 blocks
==12673==    indirectly lost: 1,786 bytes in 2 blocks
==12673==      possibly lost: 0 bytes in 0 blocks
==12673==    still reachable: 77,853 bytes in 1,066 blocks
==12673==         suppressed: 0 bytes in 0 blocks
==12673== 
==12673== ERROR SUMMARY: 2 errors from 2 contexts (suppressed: 0 from 0)
